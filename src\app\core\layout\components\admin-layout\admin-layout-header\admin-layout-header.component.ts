import { TokenService } from 'src/app/features/auth/services/token.service';
import { Component, Output, EventEmitter, OnInit, OnDestroy, HostListener, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { LanguageService } from '@core/gl-services/language-services/language.service';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { NotificationServiceProxy, NotificationDto, NotificationDtoPaginatedResult } from '@core/api/api.generated';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { Subject, takeUntil, finalize } from 'rxjs';



@Component({
  selector: 'app-admin-layout-header',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FormsModule,
    ScrollingModule
  ],
  templateUrl: './admin-layout-header.component.html',
  styleUrls: ['./admin-layout-header.component.scss']
})
export class AdminLayoutHeaderComponent implements OnInit, OnDestroy {
  @Output() menuToggle = new EventEmitter<void>();
  @Output() searchEvent = new EventEmitter<string>();
  @ViewChild('notificationDropdown', { static: false }) notificationDropdown!: ElementRef;

  // User and role properties
  storedRoles: string | null;
  notificationCount = 0;
  roleName: any;
  fullName: any;

  // Search properties
  searchQuery: string = '';
  currentLang: LanguageEnum = LanguageEnum.ar;

  // Notification dropdown properties
  isNotificationDropdownOpen = false;
  notifications: NotificationDto[] = [];
  isLoadingNotifications = false;
  notificationError: string | null = null;

  // Pagination properties
  currentPage = 1;
  pageSize = 10;
  totalNotifications = 0;
  hasMoreNotifications = false;

  // Lifecycle management
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private languageService: LanguageService,
    private TokenService: TokenService,
    private notificationServiceProxy: NotificationServiceProxy,
    private elementRef: ElementRef
  ) {
    this.initializeCurrentLanguage();
    this.languageService.currentLanguageEvent
      .pipe(takeUntil(this.destroy$))
      .subscribe((lang: LanguageEnum) => {
        this.currentLang = lang;
      });

    this.storedRoles = localStorage.getItem('roles');
    this.roleName = this.TokenService.getroles();
    this.fullName = this.TokenService.getFullName();
    console.log('fullName:', this.fullName);
  }

  ngOnInit(): void {
    this.getUserNotificationUnreaded();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
  private initializeCurrentLanguage(): void {
    try {
      const storedLang = localStorage.getItem('lang');
      if (storedLang && storedLang !== '{}') {
        this.currentLang = JSON.parse(storedLang) as LanguageEnum;
      } else {
        this.currentLang = LanguageEnum.ar; // Default to Arabic
      }
    } catch (e) {
      console.error('Error parsing stored language:', e);
      this.currentLang = LanguageEnum.ar; // Default to Arabic
    }
  }
  changeLanguage(): void {
    const newLang = this.currentLang === LanguageEnum.en ? LanguageEnum.ar : LanguageEnum.en;
    this.currentLang = newLang;
    this.languageService.switchLang(this.currentLang,true);
  }

  selectLanguage(lang: string, event: Event): void {
    event.preventDefault();
    const newLang = lang === 'en' ? LanguageEnum.en : LanguageEnum.ar;
    this.currentLang = newLang;
    this.languageService.switchLang(this.currentLang, true);
  }

  getCurrentLanguageFlag(): string {
    return this.currentLang === LanguageEnum.en ? 'assets/images/en.png' : 'assets/images/ar.png';
  }

  getCurrentLanguageText(): string {
    return this.currentLang === LanguageEnum.en ? 'English' : 'عربي';
  }
  getUserNotificationUnreaded(): void {
    this.notificationServiceProxy.notitficationList().subscribe((res) => {
      this.notificationCount = res.data;
    });
  }

  // Notification dropdown methods
  toggleNotificationDropdown(): void {
    if (this.isNotificationDropdownOpen) {
      this.closeNotificationDropdown();
    } else {
      this.openNotificationDropdown();
    }
  }

  openNotificationDropdown(): void {
    this.isNotificationDropdownOpen = true;
    if (this.notifications.length === 0) {
      this.loadNotifications();
    }
  }

  closeNotificationDropdown(): void {
    this.isNotificationDropdownOpen = false;
  }

  loadNotifications(page: number = 1): void {
    if (this.isLoadingNotifications) return;

    this.isLoadingNotifications = true;
    this.notificationError = null;

    this.notificationServiceProxy
      .unReadedNotificationList(page, this.pageSize, undefined, undefined)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.isLoadingNotifications = false;
        })
      )
      .subscribe({
        next: (response: NotificationDtoPaginatedResult) => {
          if (response.successed && response.data) {
            if (page === 1) {
              this.notifications = response.data;
            } else {
              this.notifications = [...this.notifications, ...response.data];
            }

            this.currentPage = response.currentPage || page;
            this.totalNotifications = response.totalCount || 0;
            this.hasMoreNotifications = response.hasNextPage || false;
          } else {
            this.notificationError = response.message || 'Failed to load notifications';
          }
        },
        error: (error) => {
          console.error('Error loading notifications:', error);
          this.notificationError = 'Failed to load notifications';
        }
      });
  }

  loadMoreNotifications(): void {
    if (this.hasMoreNotifications && !this.isLoadingNotifications) {
      this.loadNotifications(this.currentPage + 1);
    }
  }

  markNotificationAsRead(notification: NotificationDto): void {
    // TODO: Implement mark as read API call
    notification.isRead = true;
    this.notificationCount = Math.max(0, this.notificationCount - 1);
  }

  markAllAsRead(): void {
    // TODO: Implement mark all as read API call
    this.notifications.forEach(notification => {
      notification.isRead = true;
    });
    this.notificationCount = 0;
  }

  getNotificationIcon(notification: NotificationDto): string {
    // Return appropriate icon based on notification type or module
    switch (notification.notificationModule) {
      case 1: // Fund notifications
        return 'assets/images/notify-green.png';
      case 2: // User notifications
        return 'assets/images/notify-red.png';
      default:
        return 'assets/images/notify-green.png';
    }
  }

  getNotificationTime(createdAt: Date): string {
    const now = new Date();
    const notificationDate = new Date(createdAt);
    const diffInMinutes = Math.floor((now.getTime() - notificationDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'الآن';
    } else if (diffInMinutes < 60) {
      return `منذ ${diffInMinutes} دقيقة`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `منذ ${hours} ساعة`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `منذ ${days} يوم`;
    }
  }

  // TrackBy function for virtual scrolling performance
  trackByNotificationId(index: number, notification: NotificationDto): number {
    return notification.id;
  }

  // Click outside handler
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    if (this.isNotificationDropdownOpen &&
        !this.elementRef.nativeElement.contains(event.target)) {
      this.closeNotificationDropdown();
    }
  }
  toggleSidenav() {
    this.menuToggle.emit();
  }
  isDashboard(): boolean {
    return this.router.url.includes('/admin/dashboard');
  }
  onSearch(): void {
    if (this.searchQuery.trim()) {
      this.searchEvent.emit(this.searchQuery);
    }
  }

  // Navigate to user profile view
  navigateToProfile(): void {
    this.router.navigate(['/admin/user-management/my-profile']);
  }
}
