@import "../../../../../../assets/scss/variables";
.form-control:focus{
  box-shadow: none !important;
}

.header-logo {
  img {
    height: 32px;
    width: auto;
  }
}

.header {
  color: $navy-blue;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}
.header-padding {
  padding: 1.75rem;
}

// Mobile styles
@media (max-width: 991.98px) {
  .search-section {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .header {
    font-size: 20px; // Smaller font size on mobile
  }

  .header-logo img {
    height: 28px; // Smaller logo on mobile
  }

  .search-container .search-input {
    height: 44px;
    font-size: 14px;
  }

  .search-container .search-btn {
    width: 28px;
    height: 28px;
    right: 10px;

    svg {
      width: 16px;
      height: 15px;
    }
  }

  // Adjust user profile section for mobile
  .user-profile-section {
    padding: 0px !important;

    span {
      font-size: 12px;
    }
  }

  // Language button adjustments
  .lang-btn {
    padding: 6px 10px;
    font-size: 12px;
    height: 28px;
  }
}

// Notification badge styles are defined later in the file

// Search Section Styles
.search-section {
  width: 263px;
  position: relative;
}

.search-container {
  position: relative;
  display: flex;
  width: 263px;
  padding: 0px 12px;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
  border: 2px solid #D1D1D1;
  border-radius: 12px;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 10px;

  .search-input {
    flex: 1;
    height: 48px;
    padding: 0;
    border: none;
    background: transparent;
    font-size: 16px;
    color: #333;
    &::placeholder {
      color: #999;
      font-weight: 400;
    }

    &:focus {
      outline: none;
    }
  }

  .search-btn {
    background: none;
    border: none;
    color: #4f4f4f;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
    flex-shrink: 0;

    svg {
      width: 19px;
      height: 18px;
    }

    &:hover {
      background: rgba(0, 32, 90, 0.1);

      svg path {
        fill: $navy-blue;
      }
    }
  }
}

.create-fund-btn {
  height: 40px;
  padding: 0 16px;
  background-color: $navy-blue;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  white-space: nowrap;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: center;
  }

  i {
    font-size: 12px;
  }

  &:hover {
    background-color: darken($navy-blue, 5%);
  }
}
// Notification Container and Dropdown Styles
.notification-wrapper {
  position: relative;
}

.notification-container {
  min-width: 30px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
  }

  .notification-badge {
    position: absolute;
    right: 9.25px;
    top: 3px;
    background-color: $notification-badg;
    border-radius: 50%;
    display: flex;
    padding: 2px 5px;
    min-width: 16px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
  }
}

.user-avatar {
  width: 32px;
  height: 32px;
  object-fit: cover;
}

.dropdown-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.lang-btn {
  display: flex;
  align-items: self-start;
  gap: 8px;
  font-size: 20px;
  border-color: #00205a;
  padding: 3px 12px;
  border: 1px solid;
  height: 32px;

  span {
    line-height: 1rem;
  }
}

.menu-toggle {
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: white;

  &:hover {
    background-color: #f8f9fa;
  }

  img {
    width: 24px;
    height: 24px;
  }
}

.user-profile-section {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(0, 32, 90, 0.05);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  img {
    transition: all 0.3s ease;
  }

  &:hover img {
    box-shadow: 0 2px 8px rgba(0, 32, 90, 0.2);
  }
  span{
    text-wrap-mode: nowrap;
  }
}

// Language Dropdown Styles
.lang-btn {
  background: transparent;
  border: 1px solid #E0E0E0;
  color: $navy-blue;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
    border-color: $navy-blue;
  }

  &:focus {
    box-shadow: none;
    border-color: $navy-blue;
  }

  .language-flag {
    width: 16px;
    height: 16px;
    object-fit: cover;
    border-radius: 2px;
  }

  .language-text {
    font-size: 14px;
    font-weight: 500;
  }
}

.dropdown-menu {
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 8px 0;
  min-width: 140px;

  .dropdown-item {
    padding: 8px 16px;
    font-size: 14px;
    color: $navy-blue;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
      color: $navy-blue;
    }

    &.active {
      background-color: $navy-blue;
      color: white;

      .language-flag svg path {
        opacity: 0.9;
      }
    }

    .language-flag {
      width: 16px;
      height: 16px;
      object-fit: cover;
      border-radius: 2px;
    }
  }
}

// Notification Dropdown Styles
.notification-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 380px;
  max-height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 32, 90, 0.15);
  border: 1px solid rgba(0, 32, 90, 0.1);
  z-index: 1050;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  // Arrow pointing up to the notification icon
  &::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
    filter: drop-shadow(0 -2px 4px rgba(0, 32, 90, 0.1));
  }

  // RTL Support
  html[dir="rtl"] & {
    right: auto;
    left: 0;

    &::before {
      right: auto;
      left: 20px;
    }
  }

  @media (max-width: 768px) {
    width: 320px;
    max-height: 400px;
    right: -50px;

    html[dir="rtl"] & {
      right: auto;
      left: -50px;
    }
  }

  @media (max-width: 480px) {
    width: 280px;
    right: -80px;

    html[dir="rtl"] & {
      right: auto;
      left: -80px;
    }
  }
}

.notification-dropdown-header {
  padding: 16px 20px 12px;
  border-bottom: 1px solid #f0f0f0;

  .notification-title {
    color: $navy-blue;
    font-size: 16px;
    font-weight: 600;
  }

  .mark-all-read-btn {
    color: $primary-color1;
    font-size: 14px;
    font-weight: 500;
    border: none;
    background: none;
    padding: 0;

    &:hover {
      color: darken($primary-color1, 10%);
      text-decoration: underline !important;
    }
  }
}

.notification-list-container {
  max-height: 360px;
  overflow: hidden;
}

.notification-virtual-scroll {
  height: 100%;
  max-height: 360px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 20px;
  border-bottom: 1px solid #f8f9fa;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background-color: #f8f9fa;
  }

  &.unread {
    background-color: rgba(0, 32, 90, 0.02);

    &:hover {
      background-color: rgba(0, 32, 90, 0.05);
    }
  }

  &:last-child {
    border-bottom: none;
  }

  .notification-icon {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;

    html[dir="rtl"] & {
      margin-right: 0;
      margin-left: 12px;
    }

    img {
      width: 20px;
      height: 20px;
      object-fit: contain;
    }
  }

  .notification-content {
    flex: 1;
    min-width: 0;

    .notification-item-title {
      font-size: 14px;
      font-weight: 600;
      color: $navy-blue;
      margin: 0 0 4px 0;
      line-height: 1.3;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .notification-item-body {
      font-size: 13px;
      color: #666;
      margin: 0 0 6px 0;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .notification-time {
      font-size: 12px;
      color: #999;
      font-weight: 400;
    }
  }

  .notification-status {
    flex-shrink: 0;
    margin-left: 8px;

    html[dir="rtl"] & {
      margin-left: 0;
      margin-right: 8px;
    }

    .unread-indicator {
      width: 8px;
      height: 8px;
      background-color: $notification-badg;
      border-radius: 50%;
      display: block;
    }
  }
}

// Loading, Error, and Empty States
.notification-loading,
.notification-error,
.notification-empty {
  padding: 20px;
  text-align: center;

  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }
}

.notification-error {
  .text-danger svg {
    margin-bottom: 8px;
  }
}

.notification-empty {
  .text-muted svg {
    opacity: 0.5;
    margin-bottom: 12px;
  }
}

// Load More and Loading More
.notification-load-more {
  padding: 12px 20px;
  border-top: 1px solid #f0f0f0;
}

.notification-loading-more {
  padding: 8px 20px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

// Dropdown Footer
.notification-dropdown-footer {
  padding: 12px 20px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;

  .view-all-btn {
    color: $primary-color1;
    font-size: 14px;
    font-weight: 500;
    border: none;
    background: none;
    padding: 8px 0;

    &:hover {
      color: darken($primary-color1, 10%);
      background-color: rgba(0, 32, 90, 0.05);
      border-radius: 6px;
    }
  }
}

// Mobile Responsive Adjustments
@media (max-width: 768px) {
  .notification-dropdown {
    .notification-item {
      padding: 10px 16px;

      .notification-icon {
        width: 28px;
        height: 28px;
        margin-right: 10px;

        html[dir="rtl"] & {
          margin-right: 0;
          margin-left: 10px;
        }

        img {
          width: 18px;
          height: 18px;
        }
      }

      .notification-content {
        .notification-item-title {
          font-size: 13px;
        }

        .notification-item-body {
          font-size: 12px;
        }

        .notification-time {
          font-size: 11px;
        }
      }
    }

    .notification-dropdown-header {
      padding: 12px 16px 8px;

      .notification-title {
        font-size: 15px;
      }

      .mark-all-read-btn {
        font-size: 13px;
      }
    }

    .notification-dropdown-footer {
      padding: 10px 16px;

      .view-all-btn {
        font-size: 13px;
      }
    }
  }
}

// Animation for notification badge
.notification-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

// Smooth transitions for all interactive elements
.notification-container,
.notification-item,
.mark-all-read-btn,
.view-all-btn {
  transition: all 0.2s ease;
}

.menu-btn{
  border: none;
  background-color: transparent;
  padding: 0px;
}
