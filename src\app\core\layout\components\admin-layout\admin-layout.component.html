<div class="admin-layout" [class.sidenav-closed]="!isSidenavOpen">
	<!-- Mobile Overlay -->
	<div class="mobile-overlay" [class.show]="isMobileSidenavOpen" (click)="closeMobileSidenav()"></div>

	<app-admin-layout-side-nav
		[class.d-none]="!isSidenavOpen"
		[isMobileOpen]="isMobileSidenavOpen"
		(mobileClose)="closeMobileSidenav()">
	</app-admin-layout-side-nav>

	<div class="admin-layout-main">
		<app-admin-layout-header (menuToggle)="toggleSidenav()"></app-admin-layout-header>
		<div class="admin-layout-content">
			<router-outlet></router-outlet>
		</div>
	</div>
</div>
<div *ngIf="notification" class="custom-notification d-flex gap-2 align-items-start w-300 timeline-item">
  <div
    class="timeline-icon"
    [ngStyle]="{ backgroundColor: notification.type === 'fundNotifications' ? '#27AE6029' : '#FEEDE8' }"
  >
    <img [src]="getIconForType(notification.type)" [alt]="notification.type" />
  </div>

  <div class="timeline-content active">
    <div class="d-flex justify-content-between">
      <div>
        <h3 class="title">{{ notification.title }}</h3>
        <p class="description">{{ notification.description }}</p>
      </div>
    </div>
    <div class="timestamp">
      <span class="time">{{ notification.time }}</span>
      <!-- <span class="date">{{ notification.createdAt | dateHijriConverter }} <span class="mx-2">{{ notification.date }}</span></span> -->
    </div>
  </div>
</div>

